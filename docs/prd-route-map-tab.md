# Product Requirements Document: Route Map Tab for RFQ View

## Overview

This PRD outlines the implementation of a new "Route Map" tab in the RFQ view page that displays an interactive Google Maps route visualization showing the path between pickup and delivery locations, along with pre-calculated distance information. This feature will enhance the RFQ management experience by providing visual route context for logistics planning while optimizing performance through distance caching.

## User Stories

### Primary User Story
**As a logistics coordinator**, I want to view the route between pickup and delivery locations on an interactive map so that I can better understand the transportation requirements and make informed decisions about provider selection.

### Secondary User Stories
- **As a logistics coordinator**, I want to see the calculated distance for the route so that I can validate pricing and time estimates
- **As a logistics coordinator**, I want the distance to load instantly without waiting for API calls so that I can quickly assess route feasibility
- **As a logistics coordinator**, I want the map to handle cases where coordinates are not available gracefully so that the system remains functional
- **As a logistics coordinator**, I want the route map to be responsive and consistent with the existing UI so that it feels integrated with the platform

## Technical Requirements

### Core Dependencies
- [ ] **@vis.gl/react-google-maps**: React components for Google Maps integration
- [ ] **Google Distance Matrix API**: For optimized distance calculation during RFQ creation/updates
- [ ] **Google Routes API**: For real-time route visualization and rendering
- [ ] **Existing Google Maps API Key**: Already configured as `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`

### Architecture Compliance
- [ ] **Service-Layer Pattern**: Implement route calculation service before actions/components
- [ ] **Next.js 15 App Router**: Use Server Components where possible, Client Components only when necessary
- [ ] **Steelflow UI Patterns**: Consistent with existing tab structure and loading states
- [ ] **Error Handling**: Comprehensive error boundaries and fallback states

### Data Integration
- [ ] **RFQ Data Structure**: Extract coordinates from existing RFQ pickup/delivery locations
- [ ] **Distance Caching**: Store pre-calculated distances in RFQ database table
- [ ] **Geocoding Fallback**: Use existing Google Maps service for coordinate resolution when needed
- [ ] **Location Display**: Integrate with existing `LocationDisplay` components

### Performance Optimization Strategy
- [ ] **Distance Matrix API**: Use for efficient distance calculation during RFQ lifecycle
- [ ] **Database Storage**: Cache calculated distances to minimize API calls
- [ ] **Selective Recalculation**: Trigger distance updates only when locations change
- [ ] **Fast Loading**: Display cached distances instantly without API delays

## Implementation Tasks

### Phase 1: Database Schema & Distance Caching
- [ ] **1.1** Create database migration for RFQ distance storage
  - [ ] Add `route_distance_km` column to `rfqs` table (DECIMAL(8,2))
  - [ ] Add `route_distance_calculated_at` timestamp column
  - [ ] Add `route_coordinates_hash` for change detection
  - [ ] Update RFQ schema types and validation

- [ ] **1.2** Create `distance-calculation.service.ts` in `src/lib/services/google-maps/`
  - [ ] Implement Distance Matrix API integration
  - [ ] Handle batch distance calculations
  - [ ] Add coordinate validation and geocoding fallback
  - [ ] Include comprehensive error handling and retry logic

- [ ] **1.3** Create distance-related types in `src/lib/services/google-maps/types.ts`
  - [ ] `DistanceCalculationResult` interface
  - [ ] `RouteCoordinates` interface
  - [ ] `DistanceMatrixResponse` types
  - [ ] `CachedDistanceInfo` interface

### Phase 2: RFQ Lifecycle Integration
- [ ] **2.1** Update RFQ creation workflow
  - [ ] Integrate distance calculation in `rfq.service.ts`
  - [ ] Calculate distance after coordinate resolution
  - [ ] Store distance and calculation metadata
  - [ ] Handle calculation failures gracefully

- [ ] **2.2** Update RFQ modification workflow
  - [ ] Detect location changes using coordinate hashing
  - [ ] Trigger distance recalculation when needed
  - [ ] Update distance cache and timestamps
  - [ ] Maintain calculation history for debugging

- [ ] **2.3** Create `distance.actions.ts` in `src/lib/actions/`
  - [ ] Server Action for manual distance recalculation
  - [ ] Batch distance calculation for existing RFQs
  - [ ] Distance validation and correction actions

### Phase 3: Route Visualization Components
- [ ] **3.1** Create `route-visualization.service.ts` in `src/lib/services/google-maps/`
  - [ ] Real-time route rendering using Google Routes API
  - [ ] Route polyline generation and styling
  - [ ] Marker positioning and customization
  - [ ] Map bounds calculation for optimal viewing
  - <APIProvider apiKey={API_KEY}>
    <Map className={'route-api-example'} {...mapOptions}>
      <Route
        apiClient={apiClient}
        origin={routeOrigin}
        destination={routeDestination}
        routeOptions={routeOptions}
        appearance={appearance}
      />
    </Map>

    <ControlPanel />
  </APIProvider>

- [ ] **3.2** Create `route-map.tsx` component in `src/app/dashboard/rfqs/[id]/_components/`
  - [ ] Interactive Google Maps implementation using @vis.gl/react-google-maps
  - [ ] Display cached distance from RFQ data
  - [ ] Route polyline rendering via Routes API
  - [ ] Pickup/delivery markers with custom icons
  - [ ] Loading and error states for map rendering

- [ ] **3.3** Create `route-map-skeleton.tsx` for loading states
  - [ ] Consistent with existing skeleton patterns
  - [ ] Map container placeholder
  - [ ] Distance information skeleton

### Phase 4: Tab Integration
- [ ] **4.1** Update `rfq-detail-content.tsx`
  - [ ] Add "Route Map" tab to existing tab structure
  - [ ] Update TabsList to include 3 tabs (grid-cols-3)
  - [ ] Add Route icon from lucide-react
  - [ ] Integrate RouteMap component in TabsContent
  - [ ] Pass cached distance data to component

- [ ] **4.2** Update `rfq-detail-skeleton.tsx`
  - [ ] Add third tab skeleton
  - [ ] Include route map content skeleton

### Phase 5: Data Migration & Backfill
- [ ] **5.1** Create migration script for existing RFQs
  - [ ] Batch calculate distances for existing RFQs
  - [ ] Handle coordinate resolution for legacy data
  - [ ] Implement progress tracking and error recovery
  - [ ] Validate calculated distances against manual checks

- [ ] **5.2** Enhanced coordinate handling
  - [ ] Extract pickup coordinates from RFQ data
  - [ ] Extract delivery coordinates from RFQ data
  - [ ] Implement geocoding fallback for missing coordinates
  - [ ] Handle coordinate validation and error states

### Phase 6: Testing & Polish
- [ ] **6.1** Responsive design implementation
  - [ ] Mobile-friendly map sizing
  - [ ] Tablet layout optimization
  - [ ] Desktop full-width utilization

- [ ] **6.2** Performance optimization
  - [ ] Lazy loading of map components
  - [ ] Efficient re-rendering strategies
  - [ ] Distance cache validation and refresh mechanisms

- [ ] **6.3** Accessibility improvements
  - [ ] Keyboard navigation support
  - [ ] Screen reader compatibility
  - [ ] High contrast mode support

- [ ] **6.4** Distance calculation monitoring
  - [ ] API usage tracking and alerting
  - [ ] Distance accuracy validation
  - [ ] Cache hit rate monitoring

## Acceptance Criteria

### Functional Requirements
- [ ] **Route Visualization**: Map displays accurate route between pickup and delivery locations
- [ ] **Cached Distance Display**: Shows pre-calculated route distance instantly from database
- [ ] **Interactive Map**: Users can pan, zoom, and interact with the map
- [ ] **Marker Identification**: Clear visual distinction between pickup and delivery markers
- [ ] **Tab Integration**: Seamlessly integrated as third tab alongside "Providers" and "Email History"
- [ ] **Distance Accuracy**: Cached distances match real-time calculations within 5% variance

### Technical Requirements
- [ ] **Error Handling**: Graceful handling of missing coordinates, API failures, and network issues
- [ ] **Loading States**: Instant distance display with progressive map loading
- [ ] **Responsive Design**: Works correctly on mobile, tablet, and desktop devices
- [ ] **Performance**: Distance displays immediately, map loads within 3 seconds
- [ ] **Accessibility**: Meets WCAG 2.1 AA standards
- [ ] **API Efficiency**: 90% reduction in real-time distance API calls

### Data Requirements
- [ ] **Distance Caching**: All RFQs have calculated distances stored in database
- [ ] **Coordinate Extraction**: Successfully extracts coordinates from RFQ location data
- [ ] **Geocoding Fallback**: Falls back to geocoding when coordinates are missing
- [ ] **Data Validation**: Validates coordinate data before distance calculation
- [ ] **Cache Invalidation**: Recalculates distances when locations are modified
- [ ] **Migration Completeness**: All existing RFQs have backfilled distance data

### UI/UX Requirements
- [ ] **Visual Consistency**: Matches existing Steelflow design patterns and color scheme
- [ ] **Tab Behavior**: Consistent tab switching behavior with existing tabs
- [ ] **Loading Experience**: Smooth loading transitions with appropriate skeletons
- [ ] **Error States**: User-friendly error messages with actionable guidance
- [ ] **Mobile Experience**: Optimized touch interactions and readable text on mobile devices

## Risk Mitigation

### Technical Risks
- **Google Maps API Limits**: Implement distance caching to reduce API usage by 90%
- **Distance Calculation Failures**: Graceful fallbacks and retry mechanisms for failed calculations
- **Database Performance**: Index distance columns and optimize queries for large datasets
- **Coordinate Accuracy**: Validate coordinates and provide geocoding fallbacks
- **Cache Staleness**: Implement change detection and selective recalculation

### User Experience Risks
- **Missing Location Data**: Provide clear messaging and alternative information display
- **Instant Loading**: Cached distances eliminate loading delays for distance information
- **Mobile Usability**: Ensure touch-friendly interactions and readable content
- **Data Inconsistency**: Monitor and validate cached distances against real-time calculations

### Integration Risks
- **Database Migration**: Careful rollout of schema changes with zero-downtime deployment
- **Existing Code Impact**: Minimize changes to existing RFQ components
- **Service Layer Compliance**: Ensure proper separation of concerns
- **Error Propagation**: Prevent route map errors from affecting other tabs
- **API Cost Management**: Monitor and alert on unexpected API usage spikes

## Success Metrics

### User Engagement
- **Tab Usage**: >30% of RFQ detail page views include Route Map tab interaction
- **Session Duration**: Increased time spent on RFQ detail pages
- **User Feedback**: Positive feedback on route visualization utility

### Technical Performance
- **Distance Load Time**: Cached distances display instantly (0ms) for 100% of requests
- **Map Load Time**: Route visualization loads within 3 seconds for 95% of requests
- **API Usage Reduction**: 90% reduction in Distance Matrix API calls compared to real-time approach
- **Cache Hit Rate**: >95% of distance requests served from cache
- **Error Rate**: <2% error rate for distance calculations and route visualizations
- **Database Performance**: Distance queries execute within 50ms

### Business Impact
- **Cost Efficiency**: Significant reduction in Google Maps API costs through caching
- **Decision Making**: Improved logistics planning efficiency with instant distance access
- **Provider Selection**: Enhanced context for provider evaluation
- **User Satisfaction**: Increased platform utility and user retention
- **Data Quality**: Consistent and reliable distance information across all RFQs
