# Product Requirements Document: Route Map Tab for RFQ View

## Overview

This PRD outlines the implementation of a new "Route Map" tab in the RFQ view page that displays an interactive Google Maps route visualization showing the path between pickup and delivery locations, along with distance information. This feature will enhance the RFQ management experience by providing visual route context for logistics planning.

## User Stories

### Primary User Story
**As a logistics coordinator**, I want to view the route between pickup and delivery locations on an interactive map so that I can better understand the transportation requirements and make informed decisions about provider selection.

### Secondary User Stories
- **As a logistics coordinator**, I want to see the calculated distance for the route so that I can validate pricing and time estimates
- **As a logistics coordinator**, I want the map to handle cases where coordinates are not available gracefully so that the system remains functional
- **As a logistics coordinator**, I want the route map to be responsive and consistent with the existing UI so that it feels integrated with the platform

## Technical Requirements

### Core Dependencies
- [ ] **@vis.gl/react-google-maps**: React components for Google Maps integration
- [ ] **Google Routes API**: For route calculation and rendering
- [ ] **Existing Google Maps API Key**: Already configured as `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`

### Architecture Compliance
- [ ] **Service-Layer Pattern**: Implement route calculation service before actions/components
- [ ] **Next.js 15 App Router**: Use Server Components where possible, Client Components only when necessary
- [ ] **Steelflow UI Patterns**: Consistent with existing tab structure and loading states
- [ ] **Error Handling**: Comprehensive error boundaries and fallback states

### Data Integration
- [ ] **RFQ Data Structure**: Extract coordinates from existing RFQ pickup/delivery locations
- [ ] **Geocoding Fallback**: Use existing Google Maps service for coordinate resolution when needed
- [ ] **Location Display**: Integrate with existing `LocationDisplay` components

## Implementation Tasks

### Phase 1: Foundation & Service Layer
- [ ] **1.1** Create `route-calculation.service.ts` in `src/lib/services/google-maps/`
  - [ ] Implement route calculation using Google Routes API
  - [ ] Handle distance calculation and formatting
  - [ ] Add comprehensive error handling for API failures
  - [ ] Include coordinate validation and geocoding fallback

- [ ] **1.2** Create route-related types in `src/lib/services/google-maps/types.ts`
  - [ ] `RouteCalculationResult` interface
  - [ ] `RouteCoordinates` interface  
  - [ ] `RouteError` types

- [ ] **1.3** Create `route.actions.ts` in `src/lib/actions/`
  - [ ] Server Action for route calculation
  - [ ] Integration with route calculation service
  - [ ] Proper error handling and logging

### Phase 2: UI Components
- [ ] **2.1** Create `route-map.tsx` component in `src/app/dashboard/rfqs/[id]/_components/`
  - [ ] Interactive Google Maps implementation using @vis.gl/react-google-maps
  - [ ] Route polyline rendering
  - [ ] Pickup/delivery markers
  - [ ] Distance display
  - [ ] Loading and error states

- [ ] **2.2** Create `route-map-skeleton.tsx` for loading states
  - [ ] Consistent with existing skeleton patterns
  - [ ] Map container placeholder
  - [ ] Distance information skeleton

### Phase 3: Tab Integration
- [ ] **3.1** Update `rfq-detail-content.tsx`
  - [ ] Add "Route Map" tab to existing tab structure
  - [ ] Update TabsList to include 3 tabs (grid-cols-3)
  - [ ] Add Route icon from lucide-react
  - [ ] Integrate RouteMap component in TabsContent

- [ ] **3.2** Update `rfq-detail-skeleton.tsx`
  - [ ] Add third tab skeleton
  - [ ] Include route map content skeleton

### Phase 4: Data & Coordinate Handling
- [ ] **4.1** Enhance RFQ data extraction
  - [ ] Extract pickup coordinates from RFQ data
  - [ ] Extract delivery coordinates from RFQ data
  - [ ] Implement geocoding fallback for missing coordinates
  - [ ] Handle coordinate validation

- [ ] **4.2** Error handling for missing location data
  - [ ] Graceful fallback when coordinates unavailable
  - [ ] User-friendly error messages
  - [ ] Alternative display options

### Phase 5: Testing & Polish
- [ ] **5.1** Responsive design implementation
  - [ ] Mobile-friendly map sizing
  - [ ] Tablet layout optimization
  - [ ] Desktop full-width utilization

- [ ] **5.2** Performance optimization
  - [ ] Lazy loading of map components
  - [ ] Efficient re-rendering strategies
  - [ ] API call optimization

- [ ] **5.3** Accessibility improvements
  - [ ] Keyboard navigation support
  - [ ] Screen reader compatibility
  - [ ] High contrast mode support

## Acceptance Criteria

### Functional Requirements
- [ ] **Route Visualization**: Map displays accurate route between pickup and delivery locations
- [ ] **Distance Display**: Shows calculated route distance in appropriate units (km/miles)
- [ ] **Interactive Map**: Users can pan, zoom, and interact with the map
- [ ] **Marker Identification**: Clear visual distinction between pickup and delivery markers
- [ ] **Tab Integration**: Seamlessly integrated as third tab alongside "Providers" and "Email History"

### Technical Requirements  
- [ ] **Error Handling**: Graceful handling of missing coordinates, API failures, and network issues
- [ ] **Loading States**: Proper loading indicators during route calculation
- [ ] **Responsive Design**: Works correctly on mobile, tablet, and desktop devices
- [ ] **Performance**: Map loads within 3 seconds on standard connections
- [ ] **Accessibility**: Meets WCAG 2.1 AA standards

### Data Requirements
- [ ] **Coordinate Extraction**: Successfully extracts coordinates from RFQ location data
- [ ] **Geocoding Fallback**: Falls back to geocoding when coordinates are missing
- [ ] **Data Validation**: Validates coordinate data before map rendering
- [ ] **Error Recovery**: Provides meaningful fallbacks when location data is incomplete

### UI/UX Requirements
- [ ] **Visual Consistency**: Matches existing Steelflow design patterns and color scheme
- [ ] **Tab Behavior**: Consistent tab switching behavior with existing tabs
- [ ] **Loading Experience**: Smooth loading transitions with appropriate skeletons
- [ ] **Error States**: User-friendly error messages with actionable guidance
- [ ] **Mobile Experience**: Optimized touch interactions and readable text on mobile devices

## Risk Mitigation

### Technical Risks
- **Google Maps API Limits**: Monitor API usage and implement caching strategies
- **Coordinate Accuracy**: Validate coordinates and provide geocoding fallbacks
- **Performance Impact**: Implement lazy loading and optimize rendering

### User Experience Risks  
- **Missing Location Data**: Provide clear messaging and alternative information display
- **Slow Loading**: Implement progressive loading with meaningful feedback
- **Mobile Usability**: Ensure touch-friendly interactions and readable content

### Integration Risks
- **Existing Code Impact**: Minimize changes to existing RFQ components
- **Service Layer Compliance**: Ensure proper separation of concerns
- **Error Propagation**: Prevent route map errors from affecting other tabs

## Success Metrics

### User Engagement
- **Tab Usage**: >30% of RFQ detail page views include Route Map tab interaction
- **Session Duration**: Increased time spent on RFQ detail pages
- **User Feedback**: Positive feedback on route visualization utility

### Technical Performance
- **Load Time**: Route map loads within 3 seconds for 95% of requests
- **Error Rate**: <5% error rate for route calculations
- **API Efficiency**: Optimal use of Google Maps API quotas

### Business Impact
- **Decision Making**: Improved logistics planning efficiency
- **Provider Selection**: Enhanced context for provider evaluation
- **User Satisfaction**: Increased platform utility and user retention
